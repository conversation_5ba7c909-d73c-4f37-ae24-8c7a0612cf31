/*
Problem 15: Number Pattern Printing
Topic: Control Statements (Nested Loops)

Problem Statement:
Write a Java program to print a number pattern.
Print a right triangle pattern with numbers.

Example for n = 5:
1
1 2
1 2 3
1 2 3 4
1 2 3 4 5

Example for n = 4:
1
1 2
1 2 3
1 2 3 4

Approach:
- Use nested loops
- Outer loop controls the number of rows
- Inner loop prints numbers from 1 to current row number
- Add space between numbers for better formatting
*/

import java.util.Scanner;

public class Problem15_NumberPattern {
    
    // Method to print number pattern
    public static void printNumberPattern(int n) {
        for (int i = 1; i <= n; i++) {
            // Print numbers from 1 to i
            for (int j = 1; j <= i; j++) {
                System.out.print(j);
                if (j < i) {
                    System.out.print(" ");
                }
            }
            System.out.println(); // Move to next line
        }
    }
    
    // Alternative pattern: Right triangle with same number
    public static void printSameNumberPattern(int n) {
        System.out.println("\nAlternative Pattern (Same number in each row):");
        for (int i = 1; i <= n; i++) {
            for (int j = 1; j <= i; j++) {
                System.out.print(i);
                if (j < i) {
                    System.out.print(" ");
                }
            }
            System.out.println();
        }
    }
    
    // Star pattern for comparison
    public static void printStarPattern(int n) {
        System.out.println("\nStar Pattern:");
        for (int i = 1; i <= n; i++) {
            for (int j = 1; j <= i; j++) {
                System.out.print("* ");
            }
            System.out.println();
        }
    }
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        System.out.print("Enter the number of rows: ");
        int rows = scanner.nextInt();
        
        System.out.println("\nNumber Pattern:");
        printNumberPattern(rows);
        
        // Show alternative patterns
        printSameNumberPattern(rows);
        printStarPattern(rows);
        
        scanner.close();
    }
}
