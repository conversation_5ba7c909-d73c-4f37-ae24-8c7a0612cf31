/*
Problem 3: String Reversal
Topic: Strings

Problem Statement:
Write a Java program to reverse a given string.
Given a string, return the string with characters in reverse order.

Example:
Input: "hello"
Output: "olleh"

Input: "Java"
Output: "avaJ"

Approach:
- Use StringBuilder for efficient string manipulation
- Iterate through string from last character to first
- Append each character to StringBuilder
- Convert StringBuilder to String
*/

import java.util.Scanner;

public class Problem03_StringReversal {
    
    // Method to reverse a string
    public static String reverseString(String str) {
        StringBuilder reversed = new StringBuilder();
        
        // Iterate from last character to first
        for (int i = str.length() - 1; i >= 0; i--) {
            reversed.append(str.charAt(i));
        }
        
        return reversed.toString();
    }
    
    // Alternative method using StringBuilder's reverse method
    public static String reverseStringBuiltIn(String str) {
        return new StringBuilder(str).reverse().toString();
    }
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        System.out.print("Enter a string: ");
        String input = scanner.nextLine();
        
        String result = reverseString(input);
        
        System.out.println("Original string: " + input);
        System.out.println("Reversed string: " + result);
        
        scanner.close();
    }
}
