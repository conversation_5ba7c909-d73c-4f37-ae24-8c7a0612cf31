import java.util.Scanner;

/**
 * Problem 5: Reverse String
 * 
 * This program demonstrates:
 * - String manipulation techniques
 * - StringBuilder usage
 * - Character array operations
 * - Multiple approaches to solve the same problem
 * 
 * Approaches:
 * 1. Using StringBuilder (most efficient)
 * 2. Using character array
 * 3. Using string concatenation
 * 4. Using recursion
 */
public class Solution {
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        System.out.print("Enter a string: ");
        String input = scanner.nextLine();
        
        String reversed = reverseStringBuilder(input);
        System.out.println("Reversed string: " + reversed);
        
        scanner.close();
    }
    
    /**
     * Method 1: Using StringBuilder (Most Efficient)
     * Time Complexity: O(n)
     * Space Complexity: O(n)
     */
    public static String reverseStringBuilder(String str) {
        StringBuilder sb = new StringBuilder(str);
        return sb.reverse().toString();
    }
    
    /**
     * Method 2: Using Character Array
     * Time Complexity: O(n)
     * Space Complexity: O(n)
     */
    public static String reverseCharArray(String str) {
        char[] charArray = str.toCharArray();
        int left = 0;
        int right = charArray.length - 1;
        
        while (left < right) {
            // Swap characters
            char temp = charArray[left];
            charArray[left] = charArray[right];
            charArray[right] = temp;
            
            left++;
            right--;
        }
        
        return new String(charArray);
    }
    
    /**
     * Method 3: Using Loop and String Building
     * Time Complexity: O(n)
     * Space Complexity: O(n)
     */
    public static String reverseWithLoop(String str) {
        StringBuilder result = new StringBuilder();
        
        for (int i = str.length() - 1; i >= 0; i--) {
            result.append(str.charAt(i));
        }
        
        return result.toString();
    }
    
    /**
     * Method 4: Using Recursion
     * Time Complexity: O(n)
     * Space Complexity: O(n) due to call stack
     */
    public static String reverseRecursive(String str) {
        // Base case
        if (str == null || str.length() <= 1) {
            return str;
        }
        
        // Recursive case
        return str.charAt(str.length() - 1) + reverseRecursive(str.substring(0, str.length() - 1));
    }
    
    /**
     * Method 5: Using String Concatenation (Less Efficient)
     * Time Complexity: O(n²) due to string immutability
     * Space Complexity: O(n)
     */
    public static String reverseStringConcatenation(String str) {
        String reversed = "";
        
        for (int i = str.length() - 1; i >= 0; i--) {
            reversed += str.charAt(i);
        }
        
        return reversed;
    }
    
    /**
     * Demonstration of all approaches
     */
    public static void demonstrateAllApproaches() {
        Scanner scanner = new Scanner(System.in);
        String input = scanner.nextLine();
        
        System.out.println("StringBuilder: " + reverseStringBuilder(input));
        System.out.println("Char Array: " + reverseCharArray(input));
        System.out.println("Loop: " + reverseWithLoop(input));
        System.out.println("Recursive: " + reverseRecursive(input));
        
        scanner.close();
    }
}

/*
 * Interview Discussion Points:
 * 
 * 1. StringBuilder vs String: Why StringBuilder is more efficient
 * 2. String Immutability: How it affects performance in concatenation
 * 3. In-place vs New Object: Character array allows in-place reversal
 * 4. Recursion Trade-offs: Elegant but uses more memory
 * 5. Time Complexity: Why string concatenation is O(n²)
 * 6. Memory Usage: Different approaches have different space requirements
 */
