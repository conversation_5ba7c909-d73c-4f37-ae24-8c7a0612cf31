/*
Problem 21: String Capitalization (Title Case)
Topic: String Manipulation

Problem Statement:
Write a Java program to convert a string to title case.
First letter of each word should be uppercase, rest lowercase.

Example:
Input: "hello world java"
Output: "Hello World Java"

Input: "PROGRAMMING language"
Output: "Programming Language"

Approach:
- Split string by spaces
- Capitalize first letter of each word
- Join words back with spaces
- Simple and efficient solution
*/

import java.util.Scanner;

public class Problem21_StringCapitalization {
    
    // Method to convert to title case (within 10 lines)
    public static String toTitleCase(String str) {
        if (str == null || str.isEmpty()) return str;
        String[] words = str.toLowerCase().split(" ");
        StringBuilder result = new StringBuilder();
        for (String word : words) {
            if (!word.isEmpty()) {
                result.append(Character.toUpperCase(word.charAt(0)))
                      .append(word.substring(1)).append(" ");
            }
        }
        return result.toString().trim();
    }
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        System.out.print("Enter a string: ");
        String input = scanner.nextLine();
        
        String result = toTitleCase(input);
        
        System.out.println("Original: \"" + input + "\"");
        System.out.println("Title Case: \"" + result + "\"");
        
        scanner.close();
    }
}
