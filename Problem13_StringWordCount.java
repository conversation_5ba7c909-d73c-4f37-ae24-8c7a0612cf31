/*
Problem 13: Count Words in String
Topic: Strings

Problem Statement:
Write a Java program to count the number of words in a given string.
Words are separated by spaces. Handle multiple spaces between words.

Example:
Input: "Hello World Java"
Output: 3

Input: "  Java   Programming  Language  "
Output: 3

Approach:
- Trim the string to remove leading and trailing spaces
- Split the string by spaces
- Count non-empty parts
- Alternative: iterate and count transitions from space to non-space
*/

import java.util.Scanner;

public class Problem13_StringWordCount {
    
    // Method to count words using split
    public static int countWords(String str) {
        if (str == null || str.trim().isEmpty()) {
            return 0;
        }
        
        // Trim and split by one or more spaces
        String[] words = str.trim().split("\\s+");
        return words.length;
    }
    
    // Alternative method using manual counting
    public static int countWordsManual(String str) {
        if (str == null || str.trim().isEmpty()) {
            return 0;
        }
        
        str = str.trim();
        int count = 0;
        boolean inWord = false;
        
        for (int i = 0; i < str.length(); i++) {
            char ch = str.charAt(i);
            
            if (ch != ' ') {
                if (!inWord) {
                    count++;
                    inWord = true;
                }
            } else {
                inWord = false;
            }
        }
        
        return count;
    }
    
    // Method to display words
    public static void displayWords(String str) {
        if (str == null || str.trim().isEmpty()) {
            System.out.println("No words found.");
            return;
        }
        
        String[] words = str.trim().split("\\s+");
        System.out.print("Words found: ");
        for (int i = 0; i < words.length; i++) {
            System.out.print("\"" + words[i] + "\"");
            if (i < words.length - 1) {
                System.out.print(", ");
            }
        }
        System.out.println();
    }
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        System.out.print("Enter a string: ");
        String input = scanner.nextLine();
        
        int wordCount = countWords(input);
        
        System.out.println("Input string: \"" + input + "\"");
        displayWords(input);
        System.out.println("Number of words: " + wordCount);
        
        scanner.close();
    }
}
