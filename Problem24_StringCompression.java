/*
Problem 24: String Compression (Run Length Encoding)
Topic: String Manipulation

Problem Statement:
Write a Java program to compress a string using run-length encoding.
Replace consecutive identical characters with character followed by count.

Example:
Input: "aaabbc"
Output: "a3b2c1"

Input: "hello"
Output: "h1e1l2o1"

Approach:
- Count consecutive identical characters
- Append character and count to result
- Simple iteration with counter
*/

import java.util.Scanner;

public class Problem24_StringCompression {
    
    // Method to compress string (within 5 lines)
    public static String compressString(String str) {
        StringBuilder result = new StringBuilder();
        for (int i = 0, count = 1; i < str.length(); i++, count = 1) {
            while (i + 1 < str.length() && str.charAt(i) == str.charAt(i + 1)) { i++; count++; }
            result.append(str.charAt(i)).append(count);
        }
        return result.toString();
    }
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        System.out.print("Enter a string: ");
        String input = scanner.nextLine();
        
        String compressed = compressString(input);
        
        System.out.println("Original: \"" + input + "\" (Length: " + input.length() + ")");
        System.out.println("Compressed: \"" + compressed + "\" (Length: " + compressed.length() + ")");
        
        scanner.close();
    }
}
