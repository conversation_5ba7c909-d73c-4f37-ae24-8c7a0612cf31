/*
Problem 5: Factorial Calculator
Topic: Functions & Control Statements

Problem Statement:
Write a Java program to calculate the factorial of a given number.
Factorial of n (n!) = n × (n-1) × (n-2) × ... × 1
Factorial of 0 is defined as 1.

Example:
Input: 5
Output: 120 (5! = 5 × 4 × 3 × 2 × 1 = 120)

Input: 0
Output: 1

Approach:
- Use iterative approach with for loop
- Initialize result as 1
- Multiply result with each number from 1 to n
- Handle special case for 0
*/

import java.util.Scanner;

public class Problem05_FactorialCalculator {
    
    // Method to calculate factorial iteratively
    public static long calculateFactorial(int n) {
        if (n < 0) {
            throw new IllegalArgumentException("Factorial is not defined for negative numbers");
        }
        
        long factorial = 1;
        
        for (int i = 1; i <= n; i++) {
            factorial *= i;
        }
        
        return factorial;
    }
    
    // Recursive method to calculate factorial
    public static long calculateFactorialRecursive(int n) {
        if (n < 0) {
            throw new IllegalArgumentException("Factorial is not defined for negative numbers");
        }
        
        if (n == 0 || n == 1) {
            return 1;
        }
        
        return n * calculateFactorialRecursive(n - 1);
    }
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        System.out.print("Enter a number: ");
        int num = scanner.nextInt();
        
        try {
            long result = calculateFactorial(num);
            System.out.println("Factorial of " + num + " is: " + result);
        } catch (IllegalArgumentException e) {
            System.out.println("Error: " + e.getMessage());
        }
        
        scanner.close();
    }
}
