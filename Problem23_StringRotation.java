/*
Problem 23: String Rotation Checker
Topic: String Manipulation

Problem Statement:
Write a Java program to check if one string is a rotation of another string.
A string s1 is a rotation of string s2 if s1 can be obtained by moving 
some characters from the beginning of s2 to the end.

Example:
Input: s1 = "abcdef", s2 = "defabc"
Output: true (s2 is rotation of s1)

Input: s1 = "hello", s2 = "llohe"
Output: true

Input: s1 = "java", s2 = "python"
Output: false

Input: s1 = "abcd", s2 = "acbd"
Output: false (not a rotation, just rearrangement)

Approach:
- Check if lengths are equal (necessary condition)
- Concatenate s1 with itself (s1 + s1)
- Check if s2 is a substring of the concatenated string
- If yes, then s2 is a rotation of s1
*/

import java.util.Scanner;

public class Problem23_StringRotation {
    
    // Method to check if s2 is a rotation of s1
    public static boolean isRotation(String s1, String s2) {
        // Check if lengths are equal and strings are not null
        if (s1 == null || s2 == null || s1.length() != s2.length()) {
            return false;
        }
        
        // Empty strings are rotations of each other
        if (s1.length() == 0) {
            return true;
        }
        
        // Concatenate s1 with itself
        String concatenated = s1 + s1;
        
        // Check if s2 is a substring of concatenated string
        return concatenated.contains(s2);
    }
    
    // Method to find all possible rotations of a string
    public static void findAllRotations(String str) {
        System.out.println("All rotations of \"" + str + "\":");
        
        for (int i = 0; i < str.length(); i++) {
            String rotation = str.substring(i) + str.substring(0, i);
            System.out.println((i + 1) + ". " + rotation);
        }
    }
    
    // Method to rotate string by k positions to the left
    public static String rotateLeft(String str, int k) {
        if (str == null || str.length() == 0) {
            return str;
        }
        
        // Normalize k to be within string length
        k = k % str.length();
        
        return str.substring(k) + str.substring(0, k);
    }
    
    // Method to rotate string by k positions to the right
    public static String rotateRight(String str, int k) {
        if (str == null || str.length() == 0) {
            return str;
        }
        
        // Normalize k to be within string length
        k = k % str.length();
        
        return str.substring(str.length() - k) + str.substring(0, str.length() - k);
    }
    
    // Method to find rotation distance between two strings
    public static int findRotationDistance(String s1, String s2) {
        if (!isRotation(s1, s2)) {
            return -1; // Not a rotation
        }
        
        for (int i = 0; i < s1.length(); i++) {
            if (rotateLeft(s1, i).equals(s2)) {
                return i;
            }
        }
        
        return -1;
    }
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        System.out.print("Enter first string: ");
        String str1 = scanner.nextLine();
        
        System.out.print("Enter second string: ");
        String str2 = scanner.nextLine();
        
        boolean isRot = isRotation(str1, str2);
        
        System.out.println("\nString 1: \"" + str1 + "\"");
        System.out.println("String 2: \"" + str2 + "\"");
        
        if (isRot) {
            System.out.println("Result: String 2 IS a rotation of String 1");
            
            int distance = findRotationDistance(str1, str2);
            if (distance != -1) {
                System.out.println("Rotation distance: " + distance + " positions to the left");
            }
        } else {
            System.out.println("Result: String 2 is NOT a rotation of String 1");
        }
        
        // Show all rotations of first string
        System.out.println();
        findAllRotations(str1);
        
        // Demonstrate manual rotation
        System.out.print("\nEnter number of positions to rotate left: ");
        int positions = scanner.nextInt();
        String rotated = rotateLeft(str1, positions);
        System.out.println("\"" + str1 + "\" rotated " + positions + " positions left: \"" + rotated + "\"");
        
        scanner.close();
    }
}
