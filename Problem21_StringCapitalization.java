/*
Problem 21: String Capitalization (Title Case)
Topic: String Manipulation

Problem Statement:
Write a Java program to convert a string to title case.
Title case means the first letter of each word should be uppercase 
and the rest should be lowercase.

Example:
Input: "hello world java programming"
Output: "Hello World Java Programming"

Input: "tHiS iS a TeSt StRiNg"
Output: "This Is A Test String"

Input: "JAVA programming LANGUAGE"
Output: "Java Programming Language"

Approach:
- Split the string into words
- For each word, make first character uppercase and rest lowercase
- Join the words back with spaces
- Handle edge cases like empty strings and single characters
*/

import java.util.Scanner;

public class Problem21_StringCapitalization {
    
    // Method to convert string to title case
    public static String toTitleCase(String str) {
        if (str == null || str.trim().isEmpty()) {
            return str;
        }
        
        // Split by spaces (handle multiple spaces)
        String[] words = str.trim().split("\\s+");
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < words.length; i++) {
            String word = words[i];
            
            if (word.length() > 0) {
                // Capitalize first letter, lowercase the rest
                String capitalizedWord = word.substring(0, 1).toUpperCase() + 
                                       word.substring(1).toLowerCase();
                result.append(capitalizedWord);
                
                // Add space between words (except for last word)
                if (i < words.length - 1) {
                    result.append(" ");
                }
            }
        }
        
        return result.toString();
    }
    
    // Alternative method without using split
    public static String toTitleCaseManual(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        
        StringBuilder result = new StringBuilder();
        boolean capitalizeNext = true;
        
        for (int i = 0; i < str.length(); i++) {
            char ch = str.charAt(i);
            
            if (ch == ' ') {
                result.append(ch);
                capitalizeNext = true;
            } else if (capitalizeNext) {
                result.append(Character.toUpperCase(ch));
                capitalizeNext = false;
            } else {
                result.append(Character.toLowerCase(ch));
            }
        }
        
        return result.toString();
    }
    
    // Method to capitalize only first letter of entire string
    public static String capitalizeFirstLetter(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        
        return str.substring(0, 1).toUpperCase() + str.substring(1).toLowerCase();
    }
    
    // Method to toggle case of each character
    public static String toggleCase(String str) {
        StringBuilder result = new StringBuilder();
        
        for (char ch : str.toCharArray()) {
            if (Character.isUpperCase(ch)) {
                result.append(Character.toLowerCase(ch));
            } else if (Character.isLowerCase(ch)) {
                result.append(Character.toUpperCase(ch));
            } else {
                result.append(ch);
            }
        }
        
        return result.toString();
    }
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        System.out.print("Enter a string: ");
        String input = scanner.nextLine();
        
        String titleCase = toTitleCase(input);
        String firstCapital = capitalizeFirstLetter(input);
        String toggled = toggleCase(input);
        
        System.out.println("\nOriginal String: \"" + input + "\"");
        System.out.println("Title Case: \"" + titleCase + "\"");
        System.out.println("First Letter Capital: \"" + firstCapital + "\"");
        System.out.println("Toggle Case: \"" + toggled + "\"");
        
        // Demonstrate manual method
        String manualTitleCase = toTitleCaseManual(input);
        System.out.println("Manual Title Case: \"" + manualTitleCase + "\"");
        
        scanner.close();
    }
}
