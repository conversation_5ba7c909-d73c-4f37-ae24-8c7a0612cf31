# Java Interview Problems - Complete Guide
## 30 Essential Programming Questions for Freshers

---

## Table of Contents

### Fundamentals (6 Problems)
1. Sum of Two Numbers
2. Digit Sum Calculator
3. Multiplication Table Generator
4. Swap Two Numbers
5. Armstrong Number Checker
6. Simple Interest Calculator

### Control Statements (4 Problems)
7. Even or Odd Checker
8. Prime Number Checker
9. Factorial Calculator
10. Number Pattern Printing

### Strings (10 Problems)
11. String Reversal
12. Palindrome Checker
13. Vowel Counter
14. String Word Count
15. String Capitalization
16. Remove Duplicate Characters
17. First Non-Repeating Character
18. String Compression
19. String Rotation Checker
20. Count Character Types

### Arrays (6 Problems)
21. Find Maximum Element
22. Array Sum Calculator
23. Linear Search
24. Array Reversal
25. Second Largest Element
26. Array Operations

### Functions (2 Problems)
27. Fibonacci Series Generator
28. Mathematical Operations

### OOP (2 Problems)
29. Student Grade Calculator
30. Bank Account Management

---

## Problem Solutions

### 1. Sum of Two Numbers (Fundamentals)
**Problem**: Add two integers and return their sum.
**Approach**: Simple arithmetic operation using + operator.
```java
public static int addTwoNumbers(int a, int b) {
    return a + b;
}
```

### 2. Even or Odd Checker (Control Statements)
**Problem**: Check if a number is even or odd.
**Approach**: Use modulo operator (%) to check remainder.
```java
public static String checkEvenOdd(int number) {
    return (number % 2 == 0) ? "Even" : "Odd";
}
```

### 3. String Reversal (Strings)
**Problem**: Reverse a given string.
**Approach**: Iterate from last to first character.
```java
public static String reverseString(String str) {
    StringBuilder reversed = new StringBuilder();
    for (int i = str.length() - 1; i >= 0; i--) {
        reversed.append(str.charAt(i));
    }
    return reversed.toString();
}
```

### 4. Find Maximum Element (Arrays)
**Problem**: Find the largest element in an array.
**Approach**: Compare each element with current maximum.
```java
public static int findMaximum(int[] arr) {
    int max = arr[0];
    for (int i = 1; i < arr.length; i++) {
        if (arr[i] > max) max = arr[i];
    }
    return max;
}
```

### 5. Factorial Calculator (Functions)
**Problem**: Calculate factorial of a number.
**Approach**: Multiply numbers from 1 to n.
```java
public static long calculateFactorial(int n) {
    long factorial = 1;
    for (int i = 1; i <= n; i++) {
        factorial *= i;
    }
    return factorial;
}
```

### 6. Palindrome Checker (Strings)
**Problem**: Check if string reads same forwards and backwards.
**Approach**: Compare characters from both ends.
```java
public static boolean isPalindrome(String str) {
    str = str.toLowerCase();
    int left = 0, right = str.length() - 1;
    while (left < right) {
        if (str.charAt(left++) != str.charAt(right--)) return false;
    }
    return true;
}
```

### 7. Prime Number Checker (Control Statements)
**Problem**: Check if a number is prime.
**Approach**: Check divisibility up to square root.
```java
public static boolean isPrime(int n) {
    if (n <= 1) return false;
    if (n == 2) return true;
    if (n % 2 == 0) return false;
    for (int i = 3; i * i <= n; i += 2) {
        if (n % i == 0) return false;
    }
    return true;
}
```

### 8. Fibonacci Series (Functions)
**Problem**: Generate Fibonacci sequence.
**Approach**: Each number is sum of previous two.
```java
public static void generateFibonacci(int n) {
    int first = 0, second = 1;
    System.out.print(first + " " + second + " ");
    for (int i = 3; i <= n; i++) {
        int next = first + second;
        System.out.print(next + " ");
        first = second; second = next;
    }
}
```

### 9. Array Sum Calculator (Arrays)
**Problem**: Calculate sum of all array elements.
**Approach**: Iterate and add each element.
```java
public static int calculateSum(int[] arr) {
    int sum = 0;
    for (int element : arr) {
        sum += element;
    }
    return sum;
}
```

### 10. Vowel Counter (Strings)
**Problem**: Count vowels in a string.
**Approach**: Check each character against vowels.
```java
public static int countVowels(String str) {
    int count = 0;
    str = str.toLowerCase();
    for (char c : str.toCharArray()) {
        if ("aeiou".indexOf(c) != -1) count++;
    }
    return count;
}
```

### 11. Simple Calculator (OOP)
**Problem**: Create calculator class for basic operations.
**Approach**: Separate methods for each operation.
```java
class Calculator {
    public double add(double a, double b) { return a + b; }
    public double subtract(double a, double b) { return a - b; }
    public double multiply(double a, double b) { return a * b; }
    public double divide(double a, double b) { 
        if (b == 0) throw new ArithmeticException("Division by zero");
        return a / b; 
    }
}
```

### 12. Linear Search (Arrays)
**Problem**: Search for element in array.
**Approach**: Compare each element with target.
```java
public static int linearSearch(int[] arr, int target) {
    for (int i = 0; i < arr.length; i++) {
        if (arr[i] == target) return i;
    }
    return -1;
}
```

### 13. String Word Count (Strings)
**Problem**: Count words in a string.
**Approach**: Split by spaces and count parts.
```java
public static int countWords(String str) {
    if (str == null || str.trim().isEmpty()) return 0;
    return str.trim().split("\\s+").length;
}
```

### 14. Student Grade Calculator (OOP)
**Problem**: Calculate student grades and percentage.
**Approach**: Class with methods for calculations.
```java
class Student {
    private double subject1, subject2, subject3;
    
    public double getTotalMarks() { return subject1 + subject2 + subject3; }
    public double getPercentage() { return (getTotalMarks() / 300) * 100; }
    public char getGrade() {
        double percentage = getPercentage();
        if (percentage >= 90) return 'A';
        else if (percentage >= 80) return 'B';
        else if (percentage >= 70) return 'C';
        else if (percentage >= 60) return 'D';
        else return 'F';
    }
}
```

### 15. Number Pattern Printing (Control Statements)
**Problem**: Print number patterns using loops.
**Approach**: Nested loops for rows and columns.
```java
public static void printNumberPattern(int n) {
    for (int i = 1; i <= n; i++) {
        for (int j = 1; j <= i; j++) {
            System.out.print(j + " ");
        }
        System.out.println();
    }
}
```

### 16. Array Reversal (Arrays)
**Problem**: Reverse array in-place.
**Approach**: Swap elements from both ends.
```java
public static void reverseArray(int[] arr) {
    int start = 0, end = arr.length - 1;
    while (start < end) {
        int temp = arr[start];
        arr[start] = arr[end];
        arr[end] = temp;
        start++; end--;
    }
}
```

### 17. Digit Sum Calculator (Fundamentals)
**Problem**: Find sum of digits in a number.
**Approach**: Extract digits using modulo and division.
```java
public static int sumOfDigits(int number) {
    int sum = 0;
    number = Math.abs(number);
    while (number > 0) {
        sum += number % 10;
        number /= 10;
    }
    return sum;
}
```

### 18. Bank Account Management (OOP)
**Problem**: Create bank account with deposit/withdraw operations.
**Approach**: Class with balance management methods.
```java
class BankAccount {
    private double balance;
    
    public boolean deposit(double amount) {
        if (amount > 0) { balance += amount; return true; }
        return false;
    }
    
    public boolean withdraw(double amount) {
        if (amount > 0 && amount <= balance) { 
            balance -= amount; return true; 
        }
        return false;
    }
    
    public double getBalance() { return balance; }
}
```

### 19. String Anagram Checker (Strings)
**Problem**: Check if two strings are anagrams.
**Approach**: Sort characters and compare.
```java
public static boolean isAnagram(String str1, String str2) {
    if (str1.length() != str2.length()) return false;
    char[] arr1 = str1.toLowerCase().toCharArray();
    char[] arr2 = str2.toLowerCase().toCharArray();
    Arrays.sort(arr1); Arrays.sort(arr2);
    return Arrays.equals(arr1, arr2);
}
```

### 20. Multiplication Table (Fundamentals)
**Problem**: Generate multiplication table for a number.
**Approach**: Loop and multiply with each iteration.
```java
public static void generateTable(int number, int range) {
    for (int i = 1; i <= range; i++) {
        System.out.println(number + " × " + i + " = " + (number * i));
    }
}
```

### 21. String Capitalization (Strings)
**Problem**: Convert string to title case.
**Approach**: Capitalize first letter of each word.
```java
public static String toTitleCase(String str) {
    String[] words = str.toLowerCase().split(" ");
    StringBuilder result = new StringBuilder();
    for (String word : words) {
        if (!word.isEmpty()) {
            result.append(Character.toUpperCase(word.charAt(0)))
                  .append(word.substring(1)).append(" ");
        }
    }
    return result.toString().trim();
}
```

### 22. Remove Duplicate Characters (Strings)
**Problem**: Remove duplicate characters from string.
**Approach**: Track seen characters using boolean array.
```java
public static String removeDuplicates(String str) {
    StringBuilder result = new StringBuilder();
    boolean[] seen = new boolean[256];
    for (char c : str.toCharArray()) {
        if (!seen[c]) { seen[c] = true; result.append(c); }
    }
    return result.toString();
}
```

### 23. First Non-Repeating Character (Strings)
**Problem**: Find first character that appears only once.
**Approach**: Count frequencies, then find first with count 1.
```java
public static char findFirstNonRepeating(String str) {
    int[] count = new int[256];
    for (char c : str.toCharArray()) count[c]++;
    for (char c : str.toCharArray()) {
        if (count[c] == 1) return c;
    }
    return '\0';
}
```

### 24. String Compression (Strings)
**Problem**: Compress string using run-length encoding.
**Approach**: Count consecutive characters.
```java
public static String compressString(String str) {
    StringBuilder result = new StringBuilder();
    for (int i = 0, count = 1; i < str.length(); i++, count = 1) {
        while (i + 1 < str.length() && str.charAt(i) == str.charAt(i + 1)) { 
            i++; count++; 
        }
        result.append(str.charAt(i)).append(count);
    }
    return result.toString();
}
```

### 25. String Rotation Checker (Strings)
**Problem**: Check if one string is rotation of another.
**Approach**: Concatenate string with itself, check substring.
```java
public static boolean isRotation(String str1, String str2) {
    if (str1.length() != str2.length()) return false;
    String doubled = str1 + str1;
    return doubled.contains(str2);
}
```

### 26. Swap Two Numbers (Fundamentals)
**Problem**: Swap two numbers using different methods.
**Approach**: Temporary variable, arithmetic, or XOR operations.
```java
// Method 1: Using temporary variable
public static void swapWithTemp(int a, int b) {
    int temp = a; a = b; b = temp;
}

// Method 2: Without temporary variable
public static void swapWithoutTemp(int a, int b) {
    a = a + b; b = a - b; a = a - b;
}
```

### 27. Armstrong Number Checker (Fundamentals)
**Problem**: Check if number equals sum of digits raised to power of digit count.
**Approach**: Extract digits, calculate powers, sum and compare.
```java
public static boolean isArmstrong(int num) {
    int original = num, sum = 0, digits = String.valueOf(num).length();
    while (num > 0) {
        int digit = num % 10;
        sum += Math.pow(digit, digits);
        num /= 10;
    }
    return sum == original;
}
```

### 28. Second Largest Element (Arrays)
**Problem**: Find second largest element in array.
**Approach**: Track largest and second largest in single pass.
```java
public static int findSecondLargest(int[] arr) {
    int largest = Integer.MIN_VALUE, secondLargest = Integer.MIN_VALUE;
    for (int num : arr) {
        if (num > largest) { secondLargest = largest; largest = num; }
        else if (num > secondLargest && num != largest) secondLargest = num;
    }
    return secondLargest;
}
```

### 29. Count Character Types (Strings)
**Problem**: Count alphabets, digits, special characters, and spaces.
**Approach**: Use Character class methods for classification.
```java
public static void countCharacterTypes(String str) {
    int alphabets = 0, digits = 0, special = 0, spaces = 0;
    for (char c : str.toCharArray()) {
        if (Character.isLetter(c)) alphabets++;
        else if (Character.isDigit(c)) digits++;
        else if (c == ' ') spaces++;
        else special++;
    }
    System.out.println("Alphabets: " + alphabets + ", Digits: " + digits + 
                      ", Special: " + special + ", Spaces: " + spaces);
}
```

### 30. Simple Interest Calculator (Fundamentals)
**Problem**: Calculate simple and compound interest.
**Approach**: Apply mathematical formulas.
```java
public static double calculateSimpleInterest(double principal, double rate, double time) {
    return (principal * rate * time) / 100;
}

public static double calculateCompoundInterest(double principal, double rate, double time) {
    return principal * Math.pow(1 + rate/100, time) - principal;
}
```

---

## Interview Tips

### Most Frequently Asked (90%+ interviews):
- String Reversal & Palindrome
- Prime Number & Factorial
- Array Maximum & Sum
- Even/Odd & Digit Sum
- First Non-Repeating Character

### Coding Best Practices:
1. **Handle edge cases** (null, empty, negative inputs)
2. **Use meaningful variable names**
3. **Add comments for complex logic**
4. **Optimize time complexity when possible**
5. **Test with multiple examples**

### Common Interview Patterns:
- **Two Pointers**: Palindrome, Array Reversal
- **Frequency Counting**: Anagrams, Character Analysis
- **Mathematical**: Prime, Factorial, Armstrong
- **String Manipulation**: Compression, Rotation
- **OOP Concepts**: Calculator, Student, Bank Account

---

## Complexity Analysis

| Problem | Time Complexity | Space Complexity |
|---------|----------------|------------------|
| String Reversal | O(n) | O(n) |
| Prime Check | O(√n) | O(1) |
| Array Search | O(n) | O(1) |
| Factorial | O(n) | O(1) |
| Palindrome | O(n) | O(1) |
| Anagram | O(n log n) | O(1) |
| Array Sum | O(n) | O(1) |
| Fibonacci | O(n) | O(1) |

---

**Total Problems: 30**  
**Topics Covered: 6**  
**Difficulty Level: Beginner to Intermediate**  
**Perfect for: Cognizant, TCS, Infosys, Wipro Freshers**
