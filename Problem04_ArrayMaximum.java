/*
Problem 4: Find Maximum Element in Array
Topic: Arrays

Problem Statement:
Write a Java program to find the maximum element in an array of integers.
Given an array of integers, return the largest element.

Example:
Input: [3, 7, 2, 9, 1]
Output: 9

Input: [-5, -2, -8, -1]
Output: -1

Approach:
- Initialize max with first element of array
- Iterate through remaining elements
- Compare each element with current max
- Update max if current element is greater
- Return the maximum element
*/

import java.util.Scanner;

public class Problem04_ArrayMaximum {
    
    // Method to find maximum element in array
    public static int findMaximum(int[] arr) {
        if (arr.length == 0) {
            throw new IllegalArgumentException("Array cannot be empty");
        }
        
        int max = arr[0];
        
        for (int i = 1; i < arr.length; i++) {
            if (arr[i] > max) {
                max = arr[i];
            }
        }
        
        return max;
    }
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        System.out.print("Enter the size of array: ");
        int size = scanner.nextInt();
        
        int[] arr = new int[size];
        
        System.out.println("Enter " + size + " elements:");
        for (int i = 0; i < size; i++) {
            arr[i] = scanner.nextInt();
        }
        
        int maximum = findMaximum(arr);
        
        System.out.println("Maximum element in the array: " + maximum);
        
        scanner.close();
    }
}
