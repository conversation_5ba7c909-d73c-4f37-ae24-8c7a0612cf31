/*
Problem 1: Sum of Two Numbers
Topic: Fundamentals

Problem Statement:
Write a Java program to find the sum of two integers.
Given two integers a and b, return their sum.

Example:
Input: a = 5, b = 3
Output: 8

Input: a = -2, b = 7
Output: 5

Approach:
- Simple addition operation
- Handle both positive and negative numbers
- Use basic arithmetic operator (+)
*/

import java.util.Scanner;

public class Problem01_SumOfTwoNumbers {
    
    // Method to calculate sum of two numbers
    public static int addTwoNumbers(int a, int b) {
        return a + b;
    }
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        System.out.print("Enter first number: ");
        int num1 = scanner.nextInt();
        
        System.out.print("Enter second number: ");
        int num2 = scanner.nextInt();
        
        int result = addTwoNumbers(num1, num2);
        
        System.out.println("Sum of " + num1 + " and " + num2 + " is: " + result);
        
        scanner.close();
    }
}
