/*
Problem 14: Student Grade Calculator
Topic: OOP (Classes and Objects)

Problem Statement:
Create a Student class that stores student information and calculates grades.
The class should have properties for name, roll number, and marks in 3 subjects.
Calculate total marks, percentage, and grade based on percentage.

Grade Criteria:
- 90% and above: A+
- 80-89%: A
- 70-79%: B
- 60-69%: C
- 50-59%: D
- Below 50%: F

Approach:
- Create Student class with private fields (encapsulation)
- Provide constructor and getter/setter methods
- Implement methods to calculate total, percentage, and grade
- Demonstrate object creation and method calling
*/

import java.util.Scanner;

class Student {
    private String name;
    private int rollNumber;
    private double subject1;
    private double subject2;
    private double subject3;
    
    // Constructor
    public Student(String name, int rollNumber, double subject1, double subject2, double subject3) {
        this.name = name;
        this.rollNumber = rollNumber;
        this.subject1 = subject1;
        this.subject2 = subject2;
        this.subject3 = subject3;
    }
    
    // Getter methods
    public String getName() { return name; }
    public int getRollNumber() { return rollNumber; }
    public double getSubject1() { return subject1; }
    public double getSubject2() { return subject2; }
    public double getSubject3() { return subject3; }
    
    // Calculate total marks
    public double getTotalMarks() {
        return subject1 + subject2 + subject3;
    }
    
    // Calculate percentage
    public double getPercentage() {
        return (getTotalMarks() / 300) * 100;
    }
    
    // Calculate grade
    public char getGrade() {
        double percentage = getPercentage();
        
        if (percentage >= 90) return 'A';
        else if (percentage >= 80) return 'B';
        else if (percentage >= 70) return 'C';
        else if (percentage >= 60) return 'D';
        else if (percentage >= 50) return 'E';
        else return 'F';
    }
    
    // Display student details
    public void displayDetails() {
        System.out.println("\n--- Student Details ---");
        System.out.println("Name: " + name);
        System.out.println("Roll Number: " + rollNumber);
        System.out.println("Subject 1 Marks: " + subject1);
        System.out.println("Subject 2 Marks: " + subject2);
        System.out.println("Subject 3 Marks: " + subject3);
        System.out.println("Total Marks: " + getTotalMarks() + "/300");
        System.out.printf("Percentage: %.2f%%\n", getPercentage());
        System.out.println("Grade: " + getGrade());
    }
}

public class Problem14_Student {
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        System.out.print("Enter student name: ");
        String name = scanner.nextLine();
        
        System.out.print("Enter roll number: ");
        int rollNumber = scanner.nextInt();
        
        System.out.print("Enter marks for Subject 1 (out of 100): ");
        double subject1 = scanner.nextDouble();
        
        System.out.print("Enter marks for Subject 2 (out of 100): ");
        double subject2 = scanner.nextDouble();
        
        System.out.print("Enter marks for Subject 3 (out of 100): ");
        double subject3 = scanner.nextDouble();
        
        // Create student object
        Student student = new Student(name, rollNumber, subject1, subject2, subject3);
        
        // Display student details
        student.displayDetails();
        
        scanner.close();
    }
}
