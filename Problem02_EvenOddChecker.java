/*
Problem 2: Even or Odd Number Checker
Topic: Control Statements

Problem Statement:
Write a Java program to check if a given number is even or odd.
A number is even if it's divisible by 2, otherwise it's odd.

Example:
Input: 4
Output: Even

Input: 7
Output: Odd

Approach:
- Use modulo operator (%) to check remainder when divided by 2
- If remainder is 0, number is even
- If remainder is 1, number is odd
- Use if-else control statement
*/

import java.util.Scanner;

public class Problem02_EvenOddChecker {
    
    // Method to check if number is even or odd
    public static String checkEvenOdd(int number) {
        if (number % 2 == 0) {
            return "Even";
        } else {
            return "Odd";
        }
    }
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        System.out.print("Enter a number: ");
        int num = scanner.nextInt();
        
        String result = checkEvenOdd(num);
        
        System.out.println(num + " is " + result);
        
        scanner.close();
    }
}
