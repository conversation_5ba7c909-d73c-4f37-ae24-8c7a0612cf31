/*
Problem 10: Count Vowels in String
Topic: Strings & Control Statements

Problem Statement:
Write a Java program to count the number of vowels in a given string.
Vowels are: a, e, i, o, u (both uppercase and lowercase)

Example:
Input: "Hello World"
Output: 3 (e, o, o)

Input: "Programming"
Output: 3 (o, a, i)

Approach:
- Convert string to lowercase for easier comparison
- Iterate through each character
- Check if character is a vowel (a, e, i, o, u)
- Count and return the total number of vowels
*/

import java.util.Scanner;

public class Problem10_VowelCounter {
    
    // Method to count vowels in a string
    public static int countVowels(String str) {
        int count = 0;
        str = str.toLowerCase();
        
        for (int i = 0; i < str.length(); i++) {
            char ch = str.charAt(i);
            if (ch == 'a' || ch == 'e' || ch == 'i' || ch == 'o' || ch == 'u') {
                count++;
            }
        }
        
        return count;
    }
    
    // Alternative method using enhanced for loop
    public static int countVowelsEnhanced(String str) {
        int count = 0;
        str = str.toLowerCase();
        String vowels = "aeiou";
        
        for (char ch : str.toCharArray()) {
            if (vowels.indexOf(ch) != -1) {
                count++;
            }
        }
        
        return count;
    }
    
    // Method to display vowels found
    public static void displayVowels(String str) {
        str = str.toLowerCase();
        System.out.print("Vowels found: ");
        
        for (int i = 0; i < str.length(); i++) {
            char ch = str.charAt(i);
            if (ch == 'a' || ch == 'e' || ch == 'i' || ch == 'o' || ch == 'u') {
                System.out.print(ch + " ");
            }
        }
        System.out.println();
    }
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        System.out.print("Enter a string: ");
        String input = scanner.nextLine();
        
        int vowelCount = countVowels(input);
        
        System.out.println("Input string: \"" + input + "\"");
        displayVowels(input);
        System.out.println("Total number of vowels: " + vowelCount);
        
        scanner.close();
    }
}
