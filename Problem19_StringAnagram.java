/*
Problem 19: Anagram Checker
Topic: Strings & Arrays

Problem Statement:
Write a Java program to check if two strings are anagrams.
Two strings are anagrams if they contain the same characters 
with the same frequency, but in different order.

Example:
Input: "listen", "silent"
Output: true (both contain: e-1, i-1, l-1, n-1, s-1, t-1)

Input: "hello", "world"
Output: false

Input: "evil", "vile"
Output: true

Approach:
- Convert both strings to lowercase
- Sort characters of both strings
- Compare sorted strings
- Alternative: Count frequency of each character
*/

import java.util.Arrays;
import java.util.Scanner;

public class Problem19_StringAnagram {
    
    // Method to check anagram using sorting
    public static boolean isAnagramSort(String str1, String str2) {
        // Remove spaces and convert to lowercase
        str1 = str1.replaceAll("\\s", "").toLowerCase();
        str2 = str2.replaceAll("\\s", "").toLowerCase();
        
        // If lengths are different, they can't be anagrams
        if (str1.length() != str2.length()) {
            return false;
        }
        
        // Convert to character arrays and sort
        char[] arr1 = str1.toCharArray();
        char[] arr2 = str2.toCharArray();
        
        Arrays.sort(arr1);
        Arrays.sort(arr2);
        
        // Compare sorted arrays
        return Arrays.equals(arr1, arr2);
    }
    
    // Method to check anagram using character frequency
    public static boolean isAnagramFrequency(String str1, String str2) {
        str1 = str1.replaceAll("\\s", "").toLowerCase();
        str2 = str2.replaceAll("\\s", "").toLowerCase();
        
        if (str1.length() != str2.length()) {
            return false;
        }
        
        // Count frequency of characters
        int[] frequency = new int[26]; // For lowercase a-z
        
        for (int i = 0; i < str1.length(); i++) {
            frequency[str1.charAt(i) - 'a']++;
            frequency[str2.charAt(i) - 'a']--;
        }
        
        // Check if all frequencies are zero
        for (int count : frequency) {
            if (count != 0) {
                return false;
            }
        }
        
        return true;
    }
    
    // Method to display character frequency
    public static void displayCharacterFrequency(String str) {
        str = str.replaceAll("\\s", "").toLowerCase();
        int[] frequency = new int[26];
        
        for (char c : str.toCharArray()) {
            if (c >= 'a' && c <= 'z') {
                frequency[c - 'a']++;
            }
        }
        
        System.out.print("Character frequency: ");
        boolean first = true;
        for (int i = 0; i < 26; i++) {
            if (frequency[i] > 0) {
                if (!first) System.out.print(", ");
                System.out.print((char)('a' + i) + "-" + frequency[i]);
                first = false;
            }
        }
        System.out.println();
    }
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        System.out.print("Enter first string: ");
        String str1 = scanner.nextLine();
        
        System.out.print("Enter second string: ");
        String str2 = scanner.nextLine();
        
        boolean result = isAnagramSort(str1, str2);
        
        System.out.println("\nString 1: \"" + str1 + "\"");
        displayCharacterFrequency(str1);
        
        System.out.println("\nString 2: \"" + str2 + "\"");
        displayCharacterFrequency(str2);
        
        if (result) {
            System.out.println("\nResult: The strings are anagrams!");
        } else {
            System.out.println("\nResult: The strings are NOT anagrams.");
        }
        
        scanner.close();
    }
}
