/*
Problem 29: Count Different Types of Characters
Topic: Strings & Control Statements

Problem Statement:
Write a Java program to count different types of characters in a string:
- Alphabets (letters)
- Digits (numbers)
- Special characters (symbols)
- Spaces

Example:
Input: "Hello123 @World!"
Output: 
Alphabets: 10
Digits: 3
Special characters: 2
Spaces: 1

Approach:
- Iterate through each character
- Use Character class methods for classification
- Count each type separately
- Simple character classification
*/

import java.util.Scanner;

public class Problem29_CountCharacters {
    
    // Method to count character types (within 10 lines)
    public static void countCharacterTypes(String str) {
        int alphabets = 0, digits = 0, special = 0, spaces = 0;
        for (char c : str.toCharArray()) {
            if (Character.isLetter(c)) alphabets++;
            else if (Character.isDigit(c)) digits++;
            else if (c == ' ') spaces++;
            else special++;
        }
        System.out.println("Alphabets: " + alphabets + ", Digits: " + digits + 
                          ", Special: " + special + ", Spaces: " + spaces);
    }
    
    // Detailed method with separate counters
    public static void analyzeString(String str) {
        int letters = 0, digits = 0, special = 0, spaces = 0;
        int uppercase = 0, lowercase = 0;
        
        System.out.println("Character analysis for: \"" + str + "\"");
        System.out.println("Length: " + str.length());
        
        for (char c : str.toCharArray()) {
            if (Character.isLetter(c)) {
                letters++;
                if (Character.isUpperCase(c)) uppercase++;
                else lowercase++;
            } else if (Character.isDigit(c)) {
                digits++;
            } else if (c == ' ') {
                spaces++;
            } else {
                special++;
            }
        }
        
        System.out.println("\nDetailed Count:");
        System.out.println("Total Letters: " + letters);
        System.out.println("  - Uppercase: " + uppercase);
        System.out.println("  - Lowercase: " + lowercase);
        System.out.println("Digits: " + digits);
        System.out.println("Special Characters: " + special);
        System.out.println("Spaces: " + spaces);
    }
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        System.out.print("Enter a string: ");
        String input = scanner.nextLine();
        
        // Quick count
        System.out.println("\nQuick Count:");
        countCharacterTypes(input);
        
        // Detailed analysis
        System.out.println("\n" + "=".repeat(40));
        analyzeString(input);
        
        scanner.close();
    }
}
