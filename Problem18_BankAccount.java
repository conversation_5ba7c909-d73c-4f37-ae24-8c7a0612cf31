/*
Problem 18: Bank Account Management
Topic: OOP (Encapsulation, Methods)

Problem Statement:
Create a BankAccount class that manages account operations.
The class should support deposit, withdrawal, and balance inquiry.
Implement proper encapsulation and validation.

Features:
- Account number and holder name
- Balance management
- Deposit money (positive amounts only)
- Withdraw money (sufficient balance check)
- Display account details

Approach:
- Use private fields for data encapsulation
- Provide public methods for operations
- Validate inputs (positive amounts, sufficient balance)
- Demonstrate object-oriented programming concepts
*/

import java.util.Scanner;

class BankAccount {
    private String accountNumber;
    private String accountHolderName;
    private double balance;
    
    // Constructor
    public BankAccount(String accountNumber, String accountHolderName, double initialBalance) {
        this.accountNumber = accountNumber;
        this.accountHolderName = accountHolderName;
        this.balance = initialBalance >= 0 ? initialBalance : 0;
    }
    
    // Getter methods
    public String getAccountNumber() {
        return accountNumber;
    }
    
    public String getAccountHolderName() {
        return accountHolderName;
    }
    
    public double getBalance() {
        return balance;
    }
    
    // Deposit method
    public boolean deposit(double amount) {
        if (amount > 0) {
            balance += amount;
            System.out.println("Successfully deposited $" + amount);
            return true;
        } else {
            System.out.println("Invalid amount. Please enter a positive value.");
            return false;
        }
    }
    
    // Withdrawal method
    public boolean withdraw(double amount) {
        if (amount <= 0) {
            System.out.println("Invalid amount. Please enter a positive value.");
            return false;
        }
        
        if (amount > balance) {
            System.out.println("Insufficient balance. Available balance: $" + balance);
            return false;
        }
        
        balance -= amount;
        System.out.println("Successfully withdrawn $" + amount);
        return true;
    }
    
    // Display account details
    public void displayAccountDetails() {
        System.out.println("\n--- Account Details ---");
        System.out.println("Account Number: " + accountNumber);
        System.out.println("Account Holder: " + accountHolderName);
        System.out.printf("Current Balance: $%.2f\n", balance);
    }
}

public class Problem18_BankAccount {
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        // Create account
        System.out.print("Enter account number: ");
        String accountNumber = scanner.nextLine();
        
        System.out.print("Enter account holder name: ");
        String holderName = scanner.nextLine();
        
        System.out.print("Enter initial balance: ");
        double initialBalance = scanner.nextDouble();
        
        BankAccount account = new BankAccount(accountNumber, holderName, initialBalance);
        
        // Menu-driven operations
        int choice;
        do {
            System.out.println("\n--- Bank Account Menu ---");
            System.out.println("1. Display Account Details");
            System.out.println("2. Deposit Money");
            System.out.println("3. Withdraw Money");
            System.out.println("4. Check Balance");
            System.out.println("5. Exit");
            System.out.print("Enter your choice: ");
            choice = scanner.nextInt();
            
            switch (choice) {
                case 1:
                    account.displayAccountDetails();
                    break;
                case 2:
                    System.out.print("Enter deposit amount: ");
                    double depositAmount = scanner.nextDouble();
                    account.deposit(depositAmount);
                    break;
                case 3:
                    System.out.print("Enter withdrawal amount: ");
                    double withdrawAmount = scanner.nextDouble();
                    account.withdraw(withdrawAmount);
                    break;
                case 4:
                    System.out.printf("Current Balance: $%.2f\n", account.getBalance());
                    break;
                case 5:
                    System.out.println("Thank you for using our banking service!");
                    break;
                default:
                    System.out.println("Invalid choice. Please try again.");
            }
        } while (choice != 5);
        
        scanner.close();
    }
}
