/*
Problem 30: Simple Interest Calculator
Topic: Fundamentals & Functions

Problem Statement:
Write a Java program to calculate Simple Interest and Compound Interest.
Simple Interest = (Principal × Rate × Time) / 100
Compound Interest = Principal × (1 + Rate/100)^Time - Principal

Example:
Input: Principal = 1000, Rate = 5%, Time = 2 years
Simple Interest = (1000 × 5 × 2) / 100 = 100
Compound Interest = 1000 × (1.05)² - 1000 = 102.5

Approach:
- Use basic arithmetic operations
- Apply mathematical formulas
- Handle decimal calculations
- Compare both interest types
*/

import java.util.Scanner;

public class Problem30_SimpleInterest {
    
    // Method to calculate simple interest (within 10 lines)
    public static double calculateSimpleInterest(double principal, double rate, double time) {
        return (principal * rate * time) / 100;
    }
    
    // Method to calculate compound interest (within 10 lines)
    public static double calculateCompoundInterest(double principal, double rate, double time) {
        return principal * Math.pow(1 + rate/100, time) - principal;
    }
    
    // Method to display complete calculation
    public static void displayCalculations(double principal, double rate, double time) {
        double simpleInterest = calculateSimpleInterest(principal, rate, time);
        double compoundInterest = calculateCompoundInterest(principal, rate, time);
        
        System.out.println("\n==================================================");
        System.out.println("INTEREST CALCULATION RESULTS");
        System.out.println("==================================================");
        
        System.out.printf("Principal Amount: $%.2f\n", principal);
        System.out.printf("Interest Rate: %.2f%% per year\n", rate);
        System.out.printf("Time Period: %.1f years\n", time);
        
        System.out.println("\nSimple Interest Calculation:");
        System.out.printf("SI = (P × R × T) / 100\n");
        System.out.printf("SI = (%.2f × %.2f × %.1f) / 100\n", principal, rate, time);
        System.out.printf("Simple Interest = $%.2f\n", simpleInterest);
        System.out.printf("Total Amount = $%.2f\n", principal + simpleInterest);
        
        System.out.println("\nCompound Interest Calculation:");
        System.out.printf("CI = P × (1 + R/100)^T - P\n");
        System.out.printf("CI = %.2f × (1 + %.2f/100)^%.1f - %.2f\n", principal, rate, time, principal);
        System.out.printf("Compound Interest = $%.2f\n", compoundInterest);
        System.out.printf("Total Amount = $%.2f\n", principal + compoundInterest);
        
        System.out.printf("\nDifference: $%.2f\n", compoundInterest - simpleInterest);
    }
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        System.out.print("Enter Principal Amount: $");
        double principal = scanner.nextDouble();
        
        System.out.print("Enter Interest Rate (% per year): ");
        double rate = scanner.nextDouble();
        
        System.out.print("Enter Time Period (years): ");
        double time = scanner.nextDouble();
        
        displayCalculations(principal, rate, time);
        
        scanner.close();
    }
}
