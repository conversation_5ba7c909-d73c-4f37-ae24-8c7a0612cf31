/*
Problem 16: Reverse an Array
Topic: Arrays

Problem Statement:
Write a Java program to reverse an array in-place.
Given an array, reverse the order of elements without using extra space.

Example:
Input: [1, 2, 3, 4, 5]
Output: [5, 4, 3, 2, 1]

Input: [10, 20, 30]
Output: [30, 20, 10]

Approach:
- Use two pointers: start and end
- Swap elements at start and end positions
- Move start forward and end backward
- Continue until start >= end
*/

import java.util.Scanner;

public class Problem16_ArrayReverse {
    
    // Method to reverse array in-place
    public static void reverseArray(int[] arr) {
        int start = 0;
        int end = arr.length - 1;
        
        while (start < end) {
            // Swap elements at start and end positions
            int temp = arr[start];
            arr[start] = arr[end];
            arr[end] = temp;
            
            // Move pointers
            start++;
            end--;
        }
    }
    
    // Method to display array
    public static void displayArray(int[] arr, String label) {
        System.out.print(label + ": [");
        for (int i = 0; i < arr.length; i++) {
            System.out.print(arr[i]);
            if (i < arr.length - 1) {
                System.out.print(", ");
            }
        }
        System.out.println("]");
    }
    
    // Method to create a copy of array (for demonstration)
    public static int[] copyArray(int[] original) {
        int[] copy = new int[original.length];
        for (int i = 0; i < original.length; i++) {
            copy[i] = original[i];
        }
        return copy;
    }
    
    // Alternative method using recursion
    public static void reverseArrayRecursive(int[] arr, int start, int end) {
        if (start >= end) {
            return;
        }
        
        // Swap elements
        int temp = arr[start];
        arr[start] = arr[end];
        arr[end] = temp;
        
        // Recursive call
        reverseArrayRecursive(arr, start + 1, end - 1);
    }
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        System.out.print("Enter the size of array: ");
        int size = scanner.nextInt();
        
        int[] arr = new int[size];
        
        System.out.println("Enter " + size + " elements:");
        for (int i = 0; i < size; i++) {
            arr[i] = scanner.nextInt();
        }
        
        // Display original array
        displayArray(arr, "Original Array");
        
        // Reverse the array
        reverseArray(arr);
        
        // Display reversed array
        displayArray(arr, "Reversed Array");
        
        scanner.close();
    }
}
