/*
Problem 23: First Non-Repeating Character
Topic: String Manipulation

Problem Statement:
Write a Java program to find the first non-repeating character in a string.
Return the character that appears exactly once and comes first.

Example:
Input: "abccba"
Output: 'a' (first character that appears only once)

Input: "hello"
Output: 'h'

Input: "aabbcc"
Output: No non-repeating character

Approach:
- Count frequency of each character
- Find first character with frequency 1
- Use array for counting (ASCII characters)
- Two-pass solution: count then find
*/

import java.util.Scanner;

public class Problem23_FirstNonRepeating {
    
    // Method to find first non-repeating character (within 10 lines)
    public static char findFirstNonRepeating(String str) {
        if (str == null || str.isEmpty()) return '\0';
        int[] count = new int[256]; // ASCII characters
        for (char c : str.toCharArray()) count[c]++;
        for (char c : str.toCharArray()) {
            if (count[c] == 1) return c;
        }
        return '\0'; // No non-repeating character found
    }
    
    // Method to check if character exists
    public static boolean hasNonRepeating(String str) {
        return findFirstNonRepeating(str) != '\0';
    }
    
    // Method to display character frequencies
    public static void displayFrequencies(String str) {
        int[] count = new int[256];
        for (char c : str.toCharArray()) count[c]++;
        
        System.out.print("Character frequencies: ");
        for (char c : str.toCharArray()) {
            if (count[c] > 0) {
                System.out.print(c + ":" + count[c] + " ");
                count[c] = 0; // Avoid duplicates in output
            }
        }
        System.out.println();
    }
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        System.out.print("Enter a string: ");
        String input = scanner.nextLine();
        
        char result = findFirstNonRepeating(input);
        
        System.out.println("Input: \"" + input + "\"");
        displayFrequencies(input);
        
        if (result != '\0') {
            System.out.println("First non-repeating character: '" + result + "'");
        } else {
            System.out.println("No non-repeating character found");
        }
        
        scanner.close();
    }
}
