/*
Problem 9: Sum of Array Elements
Topic: Arrays

Problem Statement:
Write a Java program to calculate the sum of all elements in an array.
Given an array of integers, return the sum of all elements.

Example:
Input: [1, 2, 3, 4, 5]
Output: 15

Input: [10, -5, 3, 0, 7]
Output: 15

Approach:
- Initialize sum variable to 0
- Iterate through each element in the array
- Add each element to the sum
- Return the total sum
*/

import java.util.Scanner;

public class Problem09_ArraySum {
    
    // Method to calculate sum of array elements
    public static int calculateSum(int[] arr) {
        int sum = 0;
        
        for (int i = 0; i < arr.length; i++) {
            sum += arr[i];
        }
        
        return sum;
    }
    
    // Alternative method using enhanced for loop
    public static int calculateSumEnhanced(int[] arr) {
        int sum = 0;
        
        for (int element : arr) {
            sum += element;
        }
        
        return sum;
    }
    
    // Method to display array
    public static void displayArray(int[] arr) {
        System.out.print("Array elements: ");
        for (int i = 0; i < arr.length; i++) {
            System.out.print(arr[i]);
            if (i < arr.length - 1) {
                System.out.print(", ");
            }
        }
        System.out.println();
    }
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        System.out.print("Enter the size of array: ");
        int size = scanner.nextInt();
        
        int[] arr = new int[size];
        
        System.out.println("Enter " + size + " elements:");
        for (int i = 0; i < size; i++) {
            arr[i] = scanner.nextInt();
        }
        
        displayArray(arr);
        
        int sum = calculateSum(arr);
        
        System.out.println("Sum of all elements: " + sum);
        
        scanner.close();
    }
}
