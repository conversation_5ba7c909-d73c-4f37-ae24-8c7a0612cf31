/*
Problem 6: Palindrome Checker
Topic: Strings

Problem Statement:
Write a Java program to check if a given string is a palindrome.
A palindrome is a word that reads the same forwards and backwards.

Example:
Input: "madam"
Output: true

Input: "hello"
Output: false

Input: "racecar"
Output: true

Approach:
- Convert string to lowercase for case-insensitive comparison
- Compare characters from start and end moving towards center
- If all characters match, it's a palindrome
- Use two pointers approach
*/

import java.util.Scanner;

public class Problem06_PalindromeChecker {
    
    // Method to check if string is palindrome
    public static boolean isPalindrome(String str) {
        // Convert to lowercase for case-insensitive comparison
        str = str.toLowerCase();
        
        int left = 0;
        int right = str.length() - 1;
        
        while (left < right) {
            if (str.charAt(left) != str.charAt(right)) {
                return false;
            }
            left++;
            right--;
        }
        
        return true;
    }
    
    // Alternative method using string reversal
    public static boolean isPalindromeReverse(String str) {
        str = str.toLowerCase();
        String reversed = new StringBuilder(str).reverse().toString();
        return str.equals(reversed);
    }
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        System.out.print("Enter a string: ");
        String input = scanner.nextLine();
        
        boolean result = isPalindrome(input);
        
        if (result) {
            System.out.println("\"" + input + "\" is a palindrome.");
        } else {
            System.out.println("\"" + input + "\" is not a palindrome.");
        }
        
        scanner.close();
    }
}
