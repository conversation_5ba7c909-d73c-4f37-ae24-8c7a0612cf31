/*
Problem 22: String Compression (Run Length Encoding)
Topic: String Manipulation

Problem Statement:
Write a Java program to compress a string using run-length encoding.
Replace consecutive repeated characters with the character followed by count.
If compression doesn't reduce size, return original string.

Example:
Input: "aaabbccccaa"
Output: "a3b2c4a2"

Input: "abcdef"
Output: "abcdef" (no compression benefit)

Input: "aabbcc"
Output: "a2b2c2"

Input: "aaaa"
Output: "a4"

Approach:
- Iterate through string and count consecutive characters
- Append character and count to result
- Compare compressed length with original
- Return shorter version
*/

import java.util.Scanner;

public class Problem22_StringCompression {
    
    // Method to compress string using run-length encoding
    public static String compressString(String str) {
        if (str == null || str.length() <= 1) {
            return str;
        }
        
        StringBuilder compressed = new StringBuilder();
        int count = 1;
        char currentChar = str.charAt(0);
        
        for (int i = 1; i < str.length(); i++) {
            if (str.charAt(i) == currentChar) {
                count++;
            } else {
                // Append current character and its count
                compressed.append(currentChar);
                if (count > 1) {
                    compressed.append(count);
                }
                
                // Reset for next character
                currentChar = str.charAt(i);
                count = 1;
            }
        }
        
        // Append the last character and its count
        compressed.append(currentChar);
        if (count > 1) {
            compressed.append(count);
        }
        
        // Return compressed string only if it's shorter
        return compressed.length() < str.length() ? compressed.toString() : str;
    }
    
    // Method to decompress run-length encoded string
    public static String decompressString(String compressed) {
        if (compressed == null || compressed.isEmpty()) {
            return compressed;
        }
        
        StringBuilder decompressed = new StringBuilder();
        
        for (int i = 0; i < compressed.length(); i++) {
            char ch = compressed.charAt(i);
            
            if (Character.isLetter(ch)) {
                // Check if next character is a digit
                if (i + 1 < compressed.length() && Character.isDigit(compressed.charAt(i + 1))) {
                    int count = Character.getNumericValue(compressed.charAt(i + 1));
                    for (int j = 0; j < count; j++) {
                        decompressed.append(ch);
                    }
                    i++; // Skip the digit
                } else {
                    decompressed.append(ch);
                }
            }
        }
        
        return decompressed.toString();
    }
    
    // Method to compress with always showing count
    public static String compressStringAlwaysCount(String str) {
        if (str == null || str.length() == 0) {
            return str;
        }
        
        StringBuilder compressed = new StringBuilder();
        int count = 1;
        char currentChar = str.charAt(0);
        
        for (int i = 1; i < str.length(); i++) {
            if (str.charAt(i) == currentChar) {
                count++;
            } else {
                compressed.append(currentChar).append(count);
                currentChar = str.charAt(i);
                count = 1;
            }
        }
        
        compressed.append(currentChar).append(count);
        return compressed.toString();
    }
    
    // Method to calculate compression ratio
    public static double getCompressionRatio(String original, String compressed) {
        if (original.length() == 0) return 0;
        return ((double)(original.length() - compressed.length()) / original.length()) * 100;
    }
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        System.out.print("Enter a string to compress: ");
        String input = scanner.nextLine();
        
        String compressed = compressString(input);
        String alwaysCount = compressStringAlwaysCount(input);
        
        System.out.println("\nOriginal String: \"" + input + "\" (Length: " + input.length() + ")");
        System.out.println("Compressed String: \"" + compressed + "\" (Length: " + compressed.length() + ")");
        System.out.println("Always Show Count: \"" + alwaysCount + "\" (Length: " + alwaysCount.length() + ")");
        
        double ratio = getCompressionRatio(input, compressed);
        System.out.printf("Compression Ratio: %.2f%%\n", ratio);
        
        // Test decompression
        if (!compressed.equals(input)) {
            String decompressed = decompressString(compressed);
            System.out.println("Decompressed: \"" + decompressed + "\"");
            System.out.println("Decompression Successful: " + input.equals(decompressed));
        }
        
        scanner.close();
    }
}
