/*
Problem 22: Remove Duplicate Characters
Topic: String Manipulation

Problem Statement:
Write a Java program to remove duplicate characters from a string.
Keep only the first occurrence of each character.

Example:
Input: "programming"
Output: "progamin"

Input: "hello"
Output: "helo"

Input: "aabbcc"
Output: "abc"

Approach:
- Use StringBuilder for result
- Track seen characters using boolean array or contains method
- Add character only if not seen before
- Simple iteration approach
*/

import java.util.Scanner;

public class Problem22_RemoveDuplicates {
    
    // Method to remove duplicates (within 10 lines)
    public static String removeDuplicates(String str) {
        if (str == null || str.isEmpty()) return str;
        StringBuilder result = new StringBuilder();
        boolean[] seen = new boolean[256]; // ASCII characters
        for (char c : str.toCharArray()) {
            if (!seen[c]) {
                seen[c] = true;
                result.append(c);
            }
        }
        return result.toString();
    }
    
    // Alternative method using contains (simpler but less efficient)
    public static String removeDuplicatesSimple(String str) {
        if (str == null || str.isEmpty()) return str;
        StringBuilder result = new StringBuilder();
        for (char c : str.toCharArray()) {
            if (result.indexOf(String.valueOf(c)) == -1) {
                result.append(c);
            }
        }
        return result.toString();
    }
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        System.out.print("Enter a string: ");
        String input = scanner.nextLine();
        
        String result = removeDuplicates(input);
        
        System.out.println("Original: \"" + input + "\"");
        System.out.println("After removing duplicates: \"" + result + "\"");
        
        // Show character count comparison
        System.out.println("Original length: " + input.length());
        System.out.println("New length: " + result.length());
        
        scanner.close();
    }
}
