/*
Problem 25: String Rotation Checker
Topic: String Manipulation

Problem Statement:
Write a Java program to check if one string is a rotation of another.
A string is a rotation if it can be obtained by moving characters from 
front to back or vice versa.

Example:
Input: "abcdef", "defabc"
Output: true (rotate "abc" from front to back)

Input: "hello", "llohe"
Output: true

Input: "java", "python"
Output: false

Approach:
- Concatenate first string with itself
- Check if second string is substring of concatenated string
- Both strings must have same length
*/

import java.util.Scanner;

public class Problem25_StringRotation {
    
    // Method to check if strings are rotations (within 5 lines)
    public static boolean isRotation(String str1, String str2) {
        if (str1.length() != str2.length()) return false;
        String doubled = str1 + str1;
        return doubled.contains(str2);
    }
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        System.out.print("Enter first string: ");
        String str1 = scanner.nextLine();
        
        System.out.print("Enter second string: ");
        String str2 = scanner.nextLine();
        
        boolean result = isRotation(str1, str2);
        
        System.out.println("String 1: \"" + str1 + "\"");
        System.out.println("String 2: \"" + str2 + "\"");
        
        if (result) {
            System.out.println("Result: YES, they are rotations of each other!");
        } else {
            System.out.println("Result: NO, they are not rotations of each other.");
        }
        
        scanner.close();
    }
}
