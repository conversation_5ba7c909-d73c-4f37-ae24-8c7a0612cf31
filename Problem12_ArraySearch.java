/*
Problem 12: Linear Search in Array
Topic: Arrays & Control Statements

Problem Statement:
Write a Java program to search for a specific element in an array using linear search.
Return the index of the element if found, otherwise return -1.

Example:
Input: Array = [5, 2, 8, 1, 9], Search = 8
Output: 2 (index of element 8)

Input: Array = [3, 7, 1, 4], Search = 6
Output: -1 (element not found)

Approach:
- Iterate through each element of the array
- Compare each element with the target element
- If match found, return the index
- If no match found after complete iteration, return -1
*/

import java.util.Scanner;

public class Problem12_ArraySearch {
    
    // Method to perform linear search
    public static int linearSearch(int[] arr, int target) {
        for (int i = 0; i < arr.length; i++) {
            if (arr[i] == target) {
                return i; // Return index if element found
            }
        }
        return -1; // Return -1 if element not found
    }
    
    // Method to display array
    public static void displayArray(int[] arr) {
        System.out.print("Array: [");
        for (int i = 0; i < arr.length; i++) {
            System.out.print(arr[i]);
            if (i < arr.length - 1) {
                System.out.print(", ");
            }
        }
        System.out.println("]");
    }
    
    // Method to check if element exists
    public static boolean contains(int[] arr, int target) {
        return linearSearch(arr, target) != -1;
    }
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        System.out.print("Enter the size of array: ");
        int size = scanner.nextInt();
        
        int[] arr = new int[size];
        
        System.out.println("Enter " + size + " elements:");
        for (int i = 0; i < size; i++) {
            arr[i] = scanner.nextInt();
        }
        
        displayArray(arr);
        
        System.out.print("Enter element to search: ");
        int target = scanner.nextInt();
        
        int result = linearSearch(arr, target);
        
        if (result != -1) {
            System.out.println("Element " + target + " found at index " + result);
        } else {
            System.out.println("Element " + target + " not found in the array");
        }
        
        scanner.close();
    }
}
