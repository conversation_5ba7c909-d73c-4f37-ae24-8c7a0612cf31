/*
Problem 7: Prime Number Checker
Topic: Control Statements & Functions

Problem Statement:
Write a Java program to check if a given number is prime.
A prime number is a natural number greater than 1 that has no positive 
divisors other than 1 and itself.

Example:
Input: 7
Output: true (7 is prime)

Input: 8
Output: false (8 = 2 × 4, so not prime)

Input: 2
Output: true (2 is the smallest prime number)

Approach:
- Handle special cases: numbers <= 1 are not prime
- Check divisibility from 2 to sqrt(n)
- If any number divides n, it's not prime
- Optimize by checking only up to square root
*/

import java.util.Scanner;

public class Problem07_PrimeNumberChecker {
    
    // Method to check if number is prime
    public static boolean isPrime(int n) {
        // Numbers <= 1 are not prime
        if (n <= 1) {
            return false;
        }
        
        // 2 is the only even prime number
        if (n == 2) {
            return true;
        }
        
        // Even numbers > 2 are not prime
        if (n % 2 == 0) {
            return false;
        }
        
        // Check odd divisors up to sqrt(n)
        for (int i = 3; i * i <= n; i += 2) {
            if (n % i == 0) {
                return false;
            }
        }
        
        return true;
    }
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        System.out.print("Enter a number: ");
        int num = scanner.nextInt();
        
        boolean result = isPrime(num);
        
        if (result) {
            System.out.println(num + " is a prime number.");
        } else {
            System.out.println(num + " is not a prime number.");
        }
        
        scanner.close();
    }
}
