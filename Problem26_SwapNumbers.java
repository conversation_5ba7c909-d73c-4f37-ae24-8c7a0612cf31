/*
Problem 26: Swap Two Numbers
Topic: Fundamentals

Problem Statement:
Write a Java program to swap two numbers using different methods.
Demonstrate swapping with and without using a temporary variable.

Example:
Input: a = 5, b = 10
Output: After swap: a = 10, b = 5

Input: x = 15, y = 25
Output: After swap: x = 25, y = 15

Approach:
- Method 1: Using temporary variable (most common)
- Method 2: Using arithmetic operations (addition/subtraction)
- Method 3: Using XOR operation (bitwise)
- Simple and fundamental programming concept
*/

import java.util.Scanner;

public class Problem26_SwapNumbers {
    
    // Method 1: Swap using temporary variable (within 10 lines)
    public static void swapWithTemp(int a, int b) {
        System.out.println("Before swap: a = " + a + ", b = " + b);
        int temp = a;
        a = b;
        b = temp;
        System.out.println("After swap: a = " + a + ", b = " + b);
    }
    
    // Method 2: Swap without temporary variable using arithmetic
    public static void swapWithoutTemp(int a, int b) {
        System.out.println("Before swap: a = " + a + ", b = " + b);
        a = a + b;
        b = a - b;
        a = a - b;
        System.out.println("After swap: a = " + a + ", b = " + b);
    }
    
    // Method 3: Swap using XOR operation
    public static void swapWithXOR(int a, int b) {
        System.out.println("Before swap: a = " + a + ", b = " + b);
        a = a ^ b;
        b = a ^ b;
        a = a ^ b;
        System.out.println("After swap: a = " + a + ", b = " + b);
    }
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        System.out.print("Enter first number: ");
        int num1 = scanner.nextInt();
        
        System.out.print("Enter second number: ");
        int num2 = scanner.nextInt();
        
        System.out.println("\nMethod 1: Using temporary variable");
        swapWithTemp(num1, num2);
        
        System.out.println("\nMethod 2: Without temporary variable (arithmetic)");
        swapWithoutTemp(num1, num2);
        
        System.out.println("\nMethod 3: Using XOR operation");
        swapWithXOR(num1, num2);
        
        scanner.close();
    }
}
