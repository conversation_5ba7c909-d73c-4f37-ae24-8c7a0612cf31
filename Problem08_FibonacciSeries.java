/*
Problem 8: Fibonacci Series
Topic: Functions & Control Statements

Problem Statement:
Write a Java program to generate the first n numbers of the Fibonacci series.
Fibonacci series: 0, 1, 1, 2, 3, 5, 8, 13, 21, ...
Each number is the sum of the two preceding numbers.

Example:
Input: 7
Output: 0 1 1 2 3 5 8

Input: 5
Output: 0 1 1 2 3

Approach:
- Initialize first two numbers as 0 and 1
- Use loop to calculate subsequent numbers
- Each new number = sum of previous two numbers
- Print the series
*/

import java.util.Scanner;

public class Problem08_FibonacciSeries {
    
    // Method to generate Fibonacci series
    public static void generateFibonacci(int n) {
        if (n <= 0) {
            System.out.println("Please enter a positive number.");
            return;
        }
        
        int first = 0, second = 1;
        
        if (n >= 1) {
            System.out.print(first + " ");
        }
        if (n >= 2) {
            System.out.print(second + " ");
        }
        
        for (int i = 3; i <= n; i++) {
            int next = first + second;
            System.out.print(next + " ");
            first = second;
            second = next;
        }
        System.out.println();
    }
    
    // Method to get nth Fibonacci number
    public static int getNthFibonacci(int n) {
        if (n <= 0) return -1;
        if (n == 1) return 0;
        if (n == 2) return 1;
        
        int first = 0, second = 1;
        
        for (int i = 3; i <= n; i++) {
            int next = first + second;
            first = second;
            second = next;
        }
        
        return second;
    }
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        System.out.print("Enter the number of terms: ");
        int terms = scanner.nextInt();
        
        System.out.println("Fibonacci series with " + terms + " terms:");
        generateFibonacci(terms);
        
        scanner.close();
    }
}
