/*
Problem 28: Find Second Largest Number in Array
Topic: Arrays

Problem Statement:
Write a Java program to find the second largest element in an array.
Handle cases where all elements are same or array has less than 2 elements.

Example:
Input: [5, 2, 8, 1, 9, 3]
Output: 8 (largest = 9, second largest = 8)

Input: [10, 10, 10]
Output: No second largest (all elements are same)

Input: [7, 3]
Output: 3 (largest = 7, second largest = 3)

Approach:
- Track largest and second largest in single pass
- Update both variables as we iterate
- Handle edge cases (duplicates, insufficient elements)
*/

import java.util.Scanner;

public class Problem28_SecondLargest {
    
    // Method to find second largest (within 10 lines)
    public static int findSecondLargest(int[] arr) {
        if (arr.length < 2) return Integer.MIN_VALUE;
        int largest = Integer.MIN_VALUE, secondLargest = Integer.MIN_VALUE;
        for (int num : arr) {
            if (num > largest) { secondLargest = largest; largest = num; }
            else if (num > secondLargest && num != largest) secondLargest = num;
        }
        return secondLargest == Integer.MIN_VALUE ? Integer.MIN_VALUE : secondLargest;
    }
    
    // Method to display array
    public static void displayArray(int[] arr) {
        System.out.print("Array: [");
        for (int i = 0; i < arr.length; i++) {
            System.out.print(arr[i]);
            if (i < arr.length - 1) System.out.print(", ");
        }
        System.out.println("]");
    }
    
    // Method to find both largest and second largest
    public static void findLargestNumbers(int[] arr) {
        if (arr.length < 1) {
            System.out.println("Array is empty");
            return;
        }
        
        int largest = Integer.MIN_VALUE, secondLargest = Integer.MIN_VALUE;
        
        for (int num : arr) {
            if (num > largest) {
                secondLargest = largest;
                largest = num;
            } else if (num > secondLargest && num != largest) {
                secondLargest = num;
            }
        }
        
        System.out.println("Largest: " + largest);
        if (secondLargest != Integer.MIN_VALUE) {
            System.out.println("Second Largest: " + secondLargest);
        } else {
            System.out.println("No second largest element found");
        }
    }
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        System.out.print("Enter the size of array: ");
        int size = scanner.nextInt();
        
        if (size < 1) {
            System.out.println("Array size must be at least 1");
            scanner.close();
            return;
        }
        
        int[] arr = new int[size];
        
        System.out.println("Enter " + size + " elements:");
        for (int i = 0; i < size; i++) {
            arr[i] = scanner.nextInt();
        }
        
        displayArray(arr);
        findLargestNumbers(arr);
        
        scanner.close();
    }
}
