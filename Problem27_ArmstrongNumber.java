/*
Problem 27: Armstrong Number Checker
Topic: Fundamentals & Control Statements

Problem Statement:
Write a Java program to check if a number is an Armstrong number.
An Armstrong number is equal to the sum of its digits raised to the power 
of the number of digits.

Example:
Input: 153
Output: true (1³ + 5³ + 3³ = 1 + 125 + 27 = 153)

Input: 9474
Output: true (9⁴ + 4⁴ + 7⁴ + 4⁴ = 6561 + 256 + 2401 + 256 = 9474)

Input: 123
Output: false

Approach:
- Count number of digits
- Extract each digit and raise to power of digit count
- Sum all powered digits and compare with original number
*/

import java.util.Scanner;

public class Problem27_ArmstrongNumber {
    
    // Method to check Armstrong number (within 10 lines)
    public static boolean isArmstrong(int num) {
        int original = num, sum = 0, digits = String.valueOf(num).length();
        while (num > 0) {
            int digit = num % 10;
            sum += Math.pow(digit, digits);
            num /= 10;
        }
        return sum == original;
    }
    
    // Method to count digits
    public static int countDigits(int num) {
        return String.valueOf(Math.abs(num)).length();
    }
    
    // Method to show calculation steps
    public static void showCalculation(int num) {
        int original = num, digits = countDigits(num);
        System.out.print("Calculation: ");
        
        String calculation = "";
        int sum = 0;
        
        while (num > 0) {
            int digit = num % 10;
            int power = (int) Math.pow(digit, digits);
            sum += power;
            
            if (!calculation.isEmpty()) calculation = " + " + calculation;
            calculation = digit + "^" + digits + calculation;
            
            num /= 10;
        }
        
        System.out.println(calculation + " = " + sum);
        System.out.println("Original number: " + original);
        System.out.println("Sum: " + sum);
    }
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        System.out.print("Enter a number: ");
        int number = scanner.nextInt();
        
        boolean result = isArmstrong(number);
        
        System.out.println("\nChecking if " + number + " is an Armstrong number:");
        showCalculation(number);
        
        if (result) {
            System.out.println("Result: " + number + " is an Armstrong number!");
        } else {
            System.out.println("Result: " + number + " is NOT an Armstrong number.");
        }
        
        scanner.close();
    }
}
